"""
API to CSV Data Fetcher Service

This module handles fetching data from the API and converting it to CSV format
that matches the expected structure for the application.
"""

import pandas as pd
import json
import os
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Tuple
from pathlib import Path

# Import the integration utilities
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'src'))
from integration_utilities import PrescintoIntegrationUtilities

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class APIToCSVFetcher:
    """
    Handles fetching data from API and converting to CSV format.
    """
    
    def __init__(self):
        """Initialize the API to CSV fetcher."""
        self.integration = None
        self.client_data = None
        self._load_client_config()
        self._initialize_integration()
    
    def _load_client_config(self):
        """Load client configuration from client.json."""
        try:
            client_path = os.path.join('src', 'client.json')
            with open(client_path, 'r') as f:
                self.client_data = json.load(f)
            logger.info("Client configuration loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load client configuration: {e}")
            self.client_data = {}
    
    def _initialize_integration(self):
        """Initialize the API integration."""
        try:
            self.integration = PrescintoIntegrationUtilities()
            logger.info("API integration initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize API integration: {e}")
            self.integration = None
    
    def get_plant_info(self, plant_id: str) -> Tuple[str, str]:
        """
        Get plant information from client.json.
        
        Args:
            plant_id: Plant identifier
            
        Returns:
            Tuple of (plant_type, plant_name)
        """
        if not self.client_data:
            return "unknown", "Unknown Plant"
        
        # Search in solar plants
        for company, plants in self.client_data.get('solar', {}).items():
            for plant in plants:
                if plant.get('plant_id') == plant_id:
                    return "solar", plant.get('name', 'Unknown Plant')
        
        # Search in wind plants
        for company, plants in self.client_data.get('wind', {}).items():
            for plant in plants:
                if plant.get('plant_id') == plant_id:
                    return "wind", plant.get('name', 'Unknown Plant')
        
        return "unknown", "Unknown Plant"
    
    def fetch_generation_data(self, plant_id: str, start_date: str, end_date: str, 
                            granularity: str = "15m") -> Optional[pd.DataFrame]:
        """
        Fetch generation data from API for a specific plant.
        
        Args:
            plant_id: Plant identifier
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            granularity: Data granularity (default: 15m)
            
        Returns:
            DataFrame with generation data or None if failed
        """
        if not self.integration:
            logger.error("API integration not available")
            return None
        
        plant_type, plant_name = self.get_plant_info(plant_id)
        logger.info(f"Fetching data for {plant_name} ({plant_id}) - Type: {plant_type}")
        
        try:
            if plant_type == "solar":
                # For solar plants, fetch Daily Energy from Plant category
                df = self.integration.fetchDataV2(
                    pName=plant_id,
                    catList=["Plant"],
                    paramList=["Daily Energy"],
                    deviceList=None,
                    sDate=start_date,
                    eDate=end_date,
                    granularity=granularity,
                    condition={"Daily Energy": "last"}
                )
            elif plant_type == "wind":
                # For wind plants, fetch Generation today from Turbine category
                df = self.integration.fetchDataV2(
                    pName=plant_id,
                    catList=["Turbine"],
                    paramList=["WTUR.Generation today"],
                    deviceList=None,
                    sDate=start_date,
                    eDate=end_date,
                    granularity=granularity,
                    condition={"Generation today": "last"}
                )
            else:
                logger.error(f"Unknown plant type: {plant_type}")
                return None
            
            if df is not None and not isinstance(df, str) and not df.empty:
                logger.info(f"Successfully fetched {len(df)} rows for {plant_id}")
                return df
            else:
                logger.warning(f"No data returned for {plant_id}")
                return None
                
        except Exception as e:
            logger.error(f"API fetch failed for {plant_id}: {e}")
            return None
    
    def format_data_for_csv(self, df: pd.DataFrame, plant_id: str) -> pd.DataFrame:
        """
        Format the API data to match the expected CSV structure.
        
        Args:
            df: Raw DataFrame from API
            plant_id: Plant identifier
            
        Returns:
            Formatted DataFrame ready for CSV export
        """
        if df is None or df.empty:
            return pd.DataFrame()
        
        plant_type, plant_name = self.get_plant_info(plant_id)
        
        try:
            # Create a copy to avoid modifying original
            formatted_df = df.copy()
            
            # Ensure time column is properly formatted
            if 'time' not in formatted_df.columns:
                logger.error("No 'time' column found in API data")
                return pd.DataFrame()
            
            # Convert time to proper format
            formatted_df['time'] = pd.to_datetime(formatted_df['time'])
            
            # Find the generation data column
            generation_col = None
            for col in formatted_df.columns:
                if col != 'time' and 'Daily Energy' in col:
                    generation_col = col
                    break
                elif col != 'time' and 'Generation' in col:
                    generation_col = col
                    break
            
            if generation_col is None:
                # Use the second column if no specific generation column found
                if len(formatted_df.columns) > 1:
                    generation_col = formatted_df.columns[1]
                else:
                    logger.error("No generation data column found")
                    return pd.DataFrame()
            
            # Create the final DataFrame with the expected structure
            result_df = pd.DataFrame()
            result_df['time'] = formatted_df['time']
            result_df[f'{plant_name}.Daily Energy'] = formatted_df[generation_col]
            result_df['Plant Long Name'] = plant_name
            result_df['Plant Short Name'] = plant_id
            
            logger.info(f"Formatted {len(result_df)} rows for CSV export")
            return result_df
            
        except Exception as e:
            logger.error(f"Error formatting data for CSV: {e}")
            return pd.DataFrame()
    
    def save_to_csv(self, df: pd.DataFrame, output_path: str):
        """
        Save the formatted DataFrame to CSV file.
        
        Args:
            df: Formatted DataFrame
            output_path: Path to save the CSV file
        """
        try:
            # Ensure the directory exists
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Save to CSV
            df.to_csv(output_path, index=False)
            logger.info(f"Data saved to {output_path}")
            
        except Exception as e:
            logger.error(f"Error saving CSV file: {e}")
    
    def generate_csv_for_plant(self, plant_id: str, start_date: str, end_date: str,
                              output_path: str, granularity: str = "15m"):
        """
        Complete workflow to fetch data and generate CSV for a plant.

        Args:
            plant_id: Plant identifier
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            output_path: Path to save the CSV file
            granularity: Data granularity (default: 15m)
        """
        logger.info(f"Starting CSV generation for {plant_id}")

        # Fetch data from API
        raw_data = self.fetch_generation_data(plant_id, start_date, end_date, granularity)

        if raw_data is None or raw_data.empty:
            logger.warning(f"No data fetched from API for {plant_id}, creating template CSV")
            # Create template CSV with correct structure but no data
            template_data = self.create_template_csv(plant_id, start_date, end_date, granularity)
            self.save_to_csv(template_data, output_path)
            logger.info(f"Template CSV created for {plant_id}")
            return True

        # Format data for CSV
        formatted_data = self.format_data_for_csv(raw_data, plant_id)

        if formatted_data.empty:
            logger.error(f"Failed to format data for {plant_id}")
            return False

        # Save to CSV
        self.save_to_csv(formatted_data, output_path)

        logger.info(f"CSV generation completed for {plant_id}")
        return True

    def create_template_csv(self, plant_id: str, start_date: str, end_date: str,
                           granularity: str = "15m") -> pd.DataFrame:
        """
        Create a template CSV with the correct structure but no data.

        Args:
            plant_id: Plant identifier
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            granularity: Data granularity

        Returns:
            DataFrame with correct structure
        """
        from datetime import datetime, timedelta

        plant_type, plant_name = self.get_plant_info(plant_id)

        # Parse dates
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")

        # Generate time intervals based on granularity
        if granularity == "15m":
            interval_minutes = 15
        elif granularity == "60m" or granularity == "1h":
            interval_minutes = 60
        else:
            interval_minutes = 15  # Default

        time_points = []
        current_time = start_dt

        while current_time <= end_dt:
            time_points.append(current_time)
            current_time += timedelta(minutes=interval_minutes)

        # Create DataFrame with correct structure
        df = pd.DataFrame({
            'time': time_points,
            f'{plant_name}.Daily Energy': [None] * len(time_points),
            'Plant Long Name': [plant_name] * len(time_points),
            'Plant Short Name': [plant_id] * len(time_points)
        })

        # Format time column to match expected format
        df['time'] = df['time'].dt.strftime('%Y-%m-%dT%H:%M:%SZ+05:30')

        logger.info(f"Created template CSV with {len(df)} rows for {plant_id}")
        return df


# Global instance
api_to_csv_fetcher = APIToCSVFetcher()
