"""
Examples of how to use the new async functionality in data.py for improved performance.

This file demonstrates various ways to leverage the async functions for faster data retrieval.
"""

import asyncio
import time
from datetime import datetime, timedelta
from backend.data.data import (
    # Async functions
    get_generation_only_data_async,
    get_consumption_data_from_csv_async,
    get_plant_id_async,
    is_solar_plant_async,
    get_plants_async,
    fetch_multiple_plants_data_async,
    fetch_generation_and_consumption_async,
    
    # Synchronous wrappers for backward compatibility
    get_generation_only_data,
    get_consumption_data_from_csv,
    fetch_multiple_plants_data,
    fetch_generation_and_consumption
)

async def example_single_plant_async():
    """
    Example: Fetch data for a single plant using async functions
    """
    print("=== Single Plant Async Example ===")
    
    plant_name = "Kids Clinic India Limited"
    start_date = datetime(2024, 1, 15)
    end_date = datetime(2024, 1, 15)
    
    start_time = time.time()
    
    # Fetch generation and consumption data concurrently
    generation_df, consumption_df = await fetch_generation_and_consumption_async(
        plant_name, start_date, end_date
    )
    
    end_time = time.time()
    
    print(f"Async fetch completed in {end_time - start_time:.2f} seconds")
    print(f"Generation data: {len(generation_df)} rows")
    print(f"Consumption data: {len(consumption_df)} rows")
    print()

async def example_multiple_plants_async():
    """
    Example: Fetch data for multiple plants concurrently
    """
    print("=== Multiple Plants Async Example ===")
    
    # Get list of available plants
    plants = await get_plants_async()
    
    # Use first 3 plants for demo (or all if less than 3)
    demo_plants = plants[:3] if len(plants) >= 3 else plants
    
    start_date = datetime(2024, 1, 15)
    end_date = datetime(2024, 1, 15)
    
    start_time = time.time()
    
    # Fetch data for multiple plants concurrently
    plant_data = await fetch_multiple_plants_data_async(demo_plants, start_date, end_date)
    
    end_time = time.time()
    
    print(f"Concurrent fetch for {len(demo_plants)} plants completed in {end_time - start_time:.2f} seconds")
    
    for plant_name, df in plant_data.items():
        print(f"  {plant_name}: {len(df)} rows")
    print()

def example_sync_vs_async_comparison():
    """
    Example: Compare performance between sync and async approaches
    """
    print("=== Sync vs Async Performance Comparison ===")
    
    plant_name = "Kids Clinic India Limited"
    start_date = datetime(2024, 1, 15)
    end_date = datetime(2024, 1, 15)
    
    # Test synchronous approach
    print("Testing synchronous approach...")
    start_time = time.time()
    
    generation_df_sync = get_generation_only_data(plant_name, start_date, end_date)
    consumption_df_sync = get_consumption_data_from_csv(plant_name, start_date, end_date)
    
    sync_time = time.time() - start_time
    print(f"Synchronous fetch completed in {sync_time:.2f} seconds")
    
    # Test asynchronous approach
    print("Testing asynchronous approach...")
    start_time = time.time()
    
    generation_df_async, consumption_df_async = fetch_generation_and_consumption(
        plant_name, start_date, end_date
    )
    
    async_time = time.time() - start_time
    print(f"Asynchronous fetch completed in {async_time:.2f} seconds")
    
    # Calculate improvement
    if sync_time > 0:
        improvement = ((sync_time - async_time) / sync_time) * 100
        print(f"Performance improvement: {improvement:.1f}%")
    
    print(f"Data consistency check:")
    print(f"  Generation - Sync: {len(generation_df_sync)} rows, Async: {len(generation_df_async)} rows")
    print(f"  Consumption - Sync: {len(consumption_df_sync)} rows, Async: {len(consumption_df_async)} rows")
    print()

async def example_plant_metadata_async():
    """
    Example: Fetch plant metadata using async functions
    """
    print("=== Plant Metadata Async Example ===")
    
    # Get list of plants
    plants = await get_plants_async()
    print(f"Found {len(plants)} plants")
    
    if plants:
        # Get metadata for first plant
        plant_name = plants[0]
        
        # Fetch plant ID and type concurrently
        plant_id_task = get_plant_id_async(plant_name)
        is_solar_task = is_solar_plant_async(plant_name)
        
        plant_id, is_solar = await asyncio.gather(plant_id_task, is_solar_task)
        
        print(f"Plant: {plant_name}")
        print(f"  ID: {plant_id}")
        print(f"  Type: {'Solar' if is_solar else 'Wind'}")
    print()

async def main():
    """
    Main function to run all examples
    """
    print("Async Data Fetching Examples")
    print("=" * 50)
    
    try:
        # Run individual examples
        await example_single_plant_async()
        await example_multiple_plants_async()
        await example_plant_metadata_async()
        
        # Run performance comparison (this uses sync wrappers)
        example_sync_vs_async_comparison()
        
        print("All examples completed successfully!")
        
    except Exception as e:
        print(f"Error running examples: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Run the examples
    asyncio.run(main())
