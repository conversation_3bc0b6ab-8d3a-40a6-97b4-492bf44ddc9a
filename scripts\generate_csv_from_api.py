#!/usr/bin/env python3
"""
Generate CSV files from API data

This script fetches generation data from the API and saves it in CSV format
that matches the expected structure for the application.
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import our API to CSV fetcher
from backend.services.api_to_csv_fetcher import APIToCSVFetcher

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_client_config():
    """Load client configuration."""
    try:
        client_path = project_root / 'src' / 'client.json'
        with open(client_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Failed to load client configuration: {e}")
        return {}

def get_all_plants(client_data):
    """Get all plants from client configuration."""
    plants = []
    
    # Get solar plants
    for company, company_plants in client_data.get('solar', {}).items():
        for plant in company_plants:
            if plant.get('plant_id'):
                plants.append({
                    'plant_id': plant['plant_id'],
                    'name': plant.get('name', 'Unknown'),
                    'type': 'solar',
                    'company': company
                })
    
    # Get wind plants
    for company, company_plants in client_data.get('wind', {}).items():
        for plant in company_plants:
            if plant.get('plant_id'):
                plants.append({
                    'plant_id': plant['plant_id'],
                    'name': plant.get('name', 'Unknown'),
                    'type': 'wind',
                    'company': company
                })
    
    return plants

def generate_csv_for_kids_clinic():
    """Generate CSV specifically for Kids Clinic plant."""
    logger.info("Starting CSV generation for Kids Clinic India Limited")

    # Initialize the fetcher
    fetcher = APIToCSVFetcher()

    # Plant details
    plant_id = "IN.INTE.KIDS"

    # Use date range from the existing CSV file (2025-01-01 to 2025-01-08)
    start_date_str = "2025-01-01"
    end_date_str = "2025-01-08"

    # Output path - replace the existing file
    output_path = project_root / "Data" / "csv" / "Clod Nine generation Data.csv"

    logger.info(f"Generating CSV for {plant_id} from {start_date_str} to {end_date_str}")

    # Generate CSV (will create template if API fails)
    success = fetcher.generate_csv_for_plant(
        plant_id=plant_id,
        start_date=start_date_str,
        end_date=end_date_str,
        output_path=str(output_path),
        granularity="15m"
    )

    if success:
        logger.info(f"Successfully generated CSV for Kids Clinic: {output_path}")

        # Verify the generated file
        try:
            import pandas as pd
            df = pd.read_csv(output_path)
            logger.info(f"Generated CSV has {len(df)} rows and columns: {df.columns.tolist()}")

            # Show sample data
            logger.info("Sample generated data:")
            print(df.head())

        except Exception as e:
            logger.error(f"Error reading generated CSV: {e}")

        return True
    else:
        logger.error("Failed to generate CSV for Kids Clinic")
        return False

def generate_csv_for_all_plants():
    """Generate CSV files for all plants."""
    logger.info("Starting CSV generation for all plants")
    
    # Load client configuration
    client_data = load_client_config()
    if not client_data:
        logger.error("No client configuration available")
        return False
    
    # Get all plants
    plants = get_all_plants(client_data)
    logger.info(f"Found {len(plants)} plants to process")
    
    # Initialize the fetcher
    fetcher = APIToCSVFetcher()
    
    # Date range - last 7 days for testing
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)
    
    start_date_str = start_date.strftime("%Y-%m-%d")
    end_date_str = end_date.strftime("%Y-%m-%d")
    
    success_count = 0
    
    for plant in plants:
        plant_id = plant['plant_id']
        plant_name = plant['name']
        
        logger.info(f"Processing {plant_name} ({plant_id})")
        
        # Create output filename
        safe_name = "".join(c for c in plant_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        output_filename = f"{safe_name} generation Data.csv"
        output_path = project_root / "Data" / "csv" / output_filename
        
        # Generate CSV
        success = fetcher.generate_csv_for_plant(
            plant_id=plant_id,
            start_date=start_date_str,
            end_date=end_date_str,
            output_path=str(output_path),
            granularity="15m"
        )
        
        if success:
            success_count += 1
            logger.info(f"✓ Successfully generated CSV for {plant_name}")
        else:
            logger.error(f"✗ Failed to generate CSV for {plant_name}")
    
    logger.info(f"CSV generation completed: {success_count}/{len(plants)} plants successful")
    return success_count > 0

def test_api_connection():
    """Test API connection and data fetching."""
    logger.info("Testing API connection...")
    
    fetcher = APIToCSVFetcher()
    
    if not fetcher.integration:
        logger.error("API integration not available")
        return False
    
    # Test with Kids Clinic plant
    plant_id = "IN.INTE.KIDS"
    
    # Test date range - yesterday
    test_date = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
    
    logger.info(f"Testing data fetch for {plant_id} on {test_date}")
    
    # Fetch test data
    test_data = fetcher.fetch_generation_data(
        plant_id=plant_id,
        start_date=test_date,
        end_date=test_date,
        granularity="15m"
    )
    
    if test_data is not None and not test_data.empty:
        logger.info(f"✓ API connection successful - fetched {len(test_data)} rows")
        logger.info(f"Columns: {test_data.columns.tolist()}")
        logger.info(f"Sample data:\n{test_data.head()}")
        return True
    else:
        logger.error("✗ API connection failed or no data returned")
        return False

def main():
    """Main function."""
    logger.info("=== API to CSV Generation Script ===")

    # Test API connection first
    api_working = test_api_connection()
    if not api_working:
        logger.warning("API connection test failed. Will create template CSV instead.")
    else:
        logger.info("API connection successful. Will fetch real data.")

    # Generate CSV for Kids Clinic (the specific plant mentioned)
    if not generate_csv_for_kids_clinic():
        logger.error("Failed to generate CSV for Kids Clinic")
        return 1

    logger.info("=== CSV Generation Completed Successfully ===")
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
