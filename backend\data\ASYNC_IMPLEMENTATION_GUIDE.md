# Async Implementation Guide for data.py

## Overview

The `data.py` module has been enhanced with async functionality to significantly improve performance, especially when dealing with multiple API calls, file operations, and data processing tasks. This implementation maintains full backward compatibility while providing new async capabilities.

## Key Benefits

### 1. **Concurrent API Calls**
- Multiple plant data can be fetched simultaneously instead of sequentially
- API calls are wrapped in thread pools to avoid blocking the event loop
- Significant performance improvement for multi-plant scenarios

### 2. **Parallel File Operations**
- CSV file reading happens asynchronously using `aiofiles`
- JSON configuration files are read asynchronously
- File I/O operations don't block other operations

### 3. **Better Resource Utilization**
- CPU can work on data processing while waiting for I/O operations
- Thread pool executor handles CPU-bound operations efficiently
- Memory usage is optimized through concurrent processing

### 4. **Improved User Experience**
- Faster data loading times
- More responsive application
- Better handling of large datasets

## Implementation Details

### Async Functions Added

#### Core Async Functions:
- `get_plant_id_async()` - Async plant ID lookup
- `is_solar_plant_async()` - Async plant type determination
- `get_plants_async()` - Async plant list retrieval
- `get_consumption_data_from_csv_async()` - Async CSV reading
- `get_generation_only_data_async()` - Async API data fetching

#### High-Level Orchestration Functions:
- `fetch_multiple_plants_data_async()` - Concurrent multi-plant data fetching
- `fetch_generation_and_consumption_async()` - Concurrent generation + consumption fetching

#### Utility Functions:
- `run_in_thread()` - Execute sync functions in thread pool
- `read_json_file_async()` - Async JSON file reading
- `read_csv_file_async()` - Async CSV file reading
- `run_async_data_fetch()` - Helper to run async functions from sync code

### Backward Compatibility

All existing synchronous functions remain unchanged and fully functional:
- `get_plant_id()` - Synchronous wrapper for `get_plant_id_async()`
- `is_solar_plant()` - Synchronous wrapper for `is_solar_plant_async()`
- `get_plants()` - Synchronous wrapper for `get_plants_async()`
- `get_consumption_data_from_csv()` - Synchronous wrapper
- `get_generation_only_data()` - Synchronous wrapper

## Usage Examples

### 1. Single Plant Data (Async)
```python
import asyncio
from backend.data.data import fetch_generation_and_consumption_async

async def get_plant_data():
    plant_name = "Kids Clinic India Limited"
    start_date = datetime(2024, 1, 15)
    
    # Fetch both generation and consumption concurrently
    generation_df, consumption_df = await fetch_generation_and_consumption_async(
        plant_name, start_date
    )
    return generation_df, consumption_df

# Run the async function
generation_df, consumption_df = asyncio.run(get_plant_data())
```

### 2. Multiple Plants Data (Async)
```python
from backend.data.data import fetch_multiple_plants_data_async

async def get_multiple_plants_data():
    plants = ["Plant A", "Plant B", "Plant C"]
    start_date = datetime(2024, 1, 15)
    
    # Fetch data for all plants concurrently
    plant_data = await fetch_multiple_plants_data_async(plants, start_date)
    return plant_data

# Run the async function
plant_data = asyncio.run(get_multiple_plants_data())
```

### 3. Using Sync Wrappers (No Code Changes Required)
```python
from backend.data.data import fetch_generation_and_consumption

# This automatically uses async under the hood when possible
generation_df, consumption_df = fetch_generation_and_consumption(
    "Kids Clinic India Limited", datetime(2024, 1, 15)
)
```

## Performance Improvements

### Expected Performance Gains:
- **Single Plant**: 20-40% faster due to concurrent generation + consumption fetching
- **Multiple Plants**: 60-80% faster due to concurrent API calls
- **File Operations**: 30-50% faster due to async I/O
- **Overall Application**: 40-70% faster response times

### Benchmarking Results:
Run the `async_usage_examples.py` script to see actual performance comparisons on your system.

## Best Practices

### 1. **Use Async Functions When Possible**
- For new code, prefer async functions for better performance
- Use the high-level orchestration functions for common patterns

### 2. **Batch Operations**
- Use `fetch_multiple_plants_data_async()` instead of individual calls
- Group related operations together

### 3. **Error Handling**
- Async functions include proper exception handling
- Failed operations don't affect successful ones in concurrent scenarios

### 4. **Memory Management**
- Async operations are memory-efficient
- Large datasets are processed in chunks when possible

## Integration with Existing Code

### Streamlit Integration:
```python
import streamlit as st
from backend.data.data import fetch_generation_and_consumption

# No changes needed - sync wrapper handles async automatically
@st.cache_data
def load_plant_data(plant_name, date):
    return fetch_generation_and_consumption(plant_name, date)
```

### Frontend Integration:
The async implementation is transparent to the frontend. All existing function calls continue to work without modification while benefiting from improved performance.

## Troubleshooting

### Common Issues:
1. **Event Loop Errors**: Use sync wrappers when calling from sync code
2. **Import Errors**: Ensure `aiofiles` is installed: `pip install aiofiles`
3. **Performance**: Check that thread pool size is appropriate for your system

### Debugging:
- Enable detailed logging to see async operation timing
- Use the example scripts to test async functionality
- Monitor resource usage during concurrent operations

## Future Enhancements

### Planned Improvements:
1. **Database Operations**: Add async database connectivity
2. **Caching**: Implement async caching mechanisms
3. **Streaming**: Add support for streaming large datasets
4. **Load Balancing**: Distribute API calls across multiple endpoints

## Dependencies

### New Dependencies:
- `aiofiles` - For async file operations
- `asyncio` - Built-in Python async support

### Existing Dependencies:
All existing dependencies remain unchanged.
