"""
Testing configuration for the Energy Generation Dashboard.
This file contains settings for testing mode.
"""

# ============================================================================
# TESTING MODE CONFIGURATION
# ============================================================================

# Set to True to enable testing mode, False for production mode
TESTING_MODE = True

# Default client for testing mode
DEFAULT_TEST_CLIENT = "Kids Clinic India Limited"

# Testing mode options:
# "hidden" - Hide client selection completely
# "disabled" - Show client selection but make it disabled
TESTING_MODE_TYPE = "disabled"

# ============================================================================
# PRODUCTION MODE CONFIGURATION
# ============================================================================

# When TESTING_MODE is False, these settings will be used
PRODUCTION_ALLOW_ALL_CLIENTS = True

def get_client_selection_config():
    """
    Get the current client selection configuration based on testing mode.
    
    Returns:
        dict: Configuration dictionary with client selection settings
    """
    if TESTING_MODE:
        return {
            "mode": "testing",
            "type": TESTING_MODE_TYPE,
            "default_client": DEFAULT_TEST_CLIENT,
            "allow_selection": False
        }
    else:
        return {
            "mode": "production",
            "allow_selection": PRODUCTION_ALLOW_ALL_CLIENTS
        }

def is_testing_mode():
    """Check if the application is in testing mode."""
    return TESTING_MODE

def get_default_test_client():
    """Get the default client for testing mode."""
    return DEFAULT_TEST_CLIENT

def get_testing_mode_type():
    """Get the testing mode type (hidden or disabled)."""
    return TESTING_MODE_TYPE
