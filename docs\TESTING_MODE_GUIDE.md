# Testing Mode Configuration Guide

This guide explains how to configure the Energy Generation Dashboard for testing mode, where only "Kids Clinic India Limited" client data is available.

## Overview

The application now supports two modes:
- **Testing Mode**: Restricts client selection to "Kids Clinic India Limited" only
- **Production Mode**: Shows all available clients

## Configuration

All testing mode settings are controlled through the `src/testing_config.py` file.

### Current Configuration

```python
# Set to True to enable testing mode, False for production mode
TESTING_MODE = True

# Default client for testing mode
DEFAULT_TEST_CLIENT = "Kids Clinic India Limited"

# Testing mode options:
# "hidden" - Hide client selection completely
# "disabled" - Show client selection but make it disabled
TESTING_MODE_TYPE = "disabled"
```

## Testing Mode Options

### Option 1: Hidden Client Selection
- Client selection dropdown is completely hidden
- Shows a blue info box indicating testing mode
- Users see only the data without any client selection interface

To enable this option:
```python
TESTING_MODE_TYPE = "hidden"
```

### Option 2: Disabled Client Selection (Current)
- Client selection dropdown is visible but disabled
- Shows "Kids Clinic India Limited" as the only option
- Displays an orange warning box indicating testing mode
- Users can see the client selection but cannot change it

To enable this option:
```python
TESTING_MODE_TYPE = "disabled"
```

## Switching to Production Mode

To enable production mode with all clients:

1. Open `src/testing_config.py`
2. Change `TESTING_MODE = False`
3. Save the file
4. Restart the Streamlit application

```python
# Set to False for production mode
TESTING_MODE = False
```

## Visual Indicators

### Testing Mode (Disabled)
- Orange warning box: "Testing Mode: Client selection is locked"
- Dropdown shows only "Kids Clinic India Limited" and is disabled

### Testing Mode (Hidden)
- Blue info box: "Testing Mode: Using Kids Clinic India Limited data"
- No client selection dropdown visible

### Production Mode
- Normal client selection dropdown with all available clients
- No testing mode indicators

## Benefits

1. **Easy Testing**: Quickly restrict the application to test data only
2. **Clear Visual Feedback**: Users know when the app is in testing mode
3. **Simple Configuration**: One-line change to switch between modes
4. **No Code Changes**: Switch modes without modifying UI components
5. **Flexible Options**: Choose between hidden or disabled client selection

## Files Modified

- `frontend/components/ui_components.py`: Updated client selection logic
- `src/testing_config.py`: New configuration file for testing settings
- `docs/TESTING_MODE_GUIDE.md`: This documentation file

## Usage Examples

### For Development/Testing
Keep `TESTING_MODE = True` to work only with Kids Clinic India Limited data.

### For Production Deployment
Set `TESTING_MODE = False` to enable all clients.

### For Demo/Presentation
Use `TESTING_MODE_TYPE = "disabled"` to show the interface but restrict functionality.

## Troubleshooting

If "Kids Clinic India Limited" is not found in the client data:
- Check that the client name exactly matches the entry in `src/client.json`
- The application will fall back to showing all clients with an error message
- Verify the client data is properly loaded

## Future Enhancements

The configuration system can be extended to:
- Support multiple test clients
- Add environment-based configuration
- Include additional testing restrictions
- Support client-specific feature toggles
